import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

export default function Header() {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    document.addEventListener("scroll", handleScroll);
    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, [scrolled]);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? "bg-white shadow-md py-2" : "bg-transparent py-4"
      }`}
    >
      <div className="container mx-auto flex justify-between items-center px-4 md:px-8">
        <div className="flex items-center">
          <Image
            src="/images/logo/logo di nardo_2.png"
            alt="Di Nardo Soccorso Stradale"
            width={160}
            height={80}
            className="h-auto"
          />
        </div>
        <nav className="hidden md:flex space-x-8">
          <Link 
            href="#servizi" 
            className="text-[#0e3169] font-medium hover:text-[#23488a] transition-colors"
          >
            Servizi
          </Link>
          <Link 
            href="#chi-siamo" 
            className="text-[#0e3169] font-medium hover:text-[#23488a] transition-colors"
          >
            Chi Siamo
          </Link>
          <Link 
            href="#contatti" 
            className="text-[#0e3169] font-medium hover:text-[#23488a] transition-colors"
          >
            Contatti
          </Link>
        </nav>
        <div className="md:hidden">
          <Link
            href="tel:+393917649488"
            className="py-2 px-4 bg-[#ffcc00] text-[#0e3169] font-bold rounded-full text-sm"
          >
            Chiama Ora
          </Link>
        </div>
      </div>
    </header>
  );
}
