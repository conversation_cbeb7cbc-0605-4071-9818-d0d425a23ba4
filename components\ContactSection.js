import { FaPhone, FaMobile, FaEnvelope, FaMapMarkerAlt, FaClock } from 'react-icons/fa';


export default function ContactSection() {
  return (
    <section id="contatti" className="section-padding bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="section-title">Contattaci</h2>
          <p className="section-subtitle">
            Siamo sempre disponibili per aiutarti in qualsiasi emergenza stradale
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          <div className="bg-[#0e3169] text-white p-8 rounded-lg shadow-lg">
            <h3 className="text-2xl font-bold mb-6">Informazioni di Contatto</h3>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <FaPhone className="text-[#ffcc00] text-xl mt-1" />
                <div>
                  <h4 className="font-bold">Telefono Fisso</h4>
                  <p className="text-lg">+39 3917649488</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <FaMobile className="text-[#ffcc00] text-xl mt-1" />
                <div>
                  <h4 className="font-bold">Cellulare (24/7)</h4>
                  <p className="text-lg">+39 3917649488</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <FaEnvelope className="text-[#ffcc00] text-xl mt-1" />
                <div>
                  <h4 className="font-bold">Email</h4>
                  <p className="text-lg"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <FaMapMarkerAlt className="text-[#ffcc00] text-xl mt-1" />
                <div>
                  <h4 className="font-bold">Indirizzo</h4>
                  <p className="text-lg">Via Appia 1735 Minturno LT 04026</p>
                </div>
              </div>
                <div className="flex items-start space-x-4">
                <FaClock className="text-[#ffcc00] text-xl mt-1" />
                <div>
                  <h4 className="font-bold">Orari</h4>
                  <p className="text-lg">Attivi 24/7, tutti i giorni dell&apos;anno</p>
                </div>
              </div>
            </div>
          </div>
            <div className="h-[400px] rounded-lg overflow-hidden shadow-lg">
<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2999.788611975988!2d13.741522876459332!3d41.24816230461876!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x133adb3903b3dfad%3A0xb24754cef076e85d!2sVia%20Appia%2C%201735%2C%2004026%20Minturno%20LT!5e0!3m2!1sit!2sit!4v1747935484303!5m2!1sit!2sit" width="100%" height="100%" style={{ border: 0 }} allowFullScreen="" loading="lazy" referrerPolicy="no-referrer-when-downgrade" title="Mappa Di Nardo Soccorso Stradale"></iframe>
          </div>
        </div>
      </div>
    </section>
  );
}
