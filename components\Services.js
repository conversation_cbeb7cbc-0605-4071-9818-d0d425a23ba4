import { FaCarCrash, FaToolbox, FaRoad, FaRoute, FaShieldAlt, FaCalendarCheck } from "react-icons/fa";

const services = [
  {
    id: 1,
    title: "Soccorso Stradale",
    description: "Intervento rapido in caso di guasto o incidente stradale in ogni situazione.",
    icon: <FaCarCrash className="text-4xl text-[#0e3169] mb-4" />
  },
  {
    id: 2,
    title: "Traino Veicoli",
    description: "Rimozione e trasporto professionale del tuo veicolo fino all&apos;officina o alla destinazione scelta.",
    icon: <FaRoad className="text-4xl text-[#0e3169] mb-4" />
  },
  {
    id: 3,
    title: "Recupero Auto Incidentate",
    description: "Recupero specializzato di veicoli incidentati o ribaltati con attrezzature all&apos;avanguardia.",
    icon: <FaToolbox className="text-4xl text-[#0e3169] mb-4" />
  },
  {
    id: 4,
    title: "Assistenza in Autostrada",
    description: "Intervento rapido su tutta la rete autostradale e sulle principali arterie stradali.",
    icon: <FaRoute className="text-4xl text-[#0e3169] mb-4" />
  },
  {
    id: 5,
    title: "Servizio Garantito",
    description: "Assistenza garantita 24 ore su 24, 7 giorni su 7, anche nei giorni festivi.",
    icon: <FaShieldAlt className="text-4xl text-[#0e3169] mb-4" />
  },
  {
    id: 6,
    title: "Servizio Programmato",
    description: "Possibilità di prenotare il servizio per trasporti programmati o manutenzioni.",
    icon: <FaCalendarCheck className="text-4xl text-[#0e3169] mb-4" />
  }
];

export default function Services() {
  return (
    <section id="servizi" className="section-padding bg-[#f4f7fb]">
      <div className="container mx-auto">
        <h2 className="section-title text-center">I Nostri Servizi</h2>
        <p className="section-subtitle text-center">
          Offriamo una gamma completa di servizi di assistenza stradale, 
          sempre disponibili per risolvere ogni tua emergenza
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <div key={service.id} className="card flex flex-col items-center text-center p-6">
              {service.icon}
              <h3 className="text-xl font-bold text-[#0e3169] mb-3">{service.title}</h3>
              <p className="text-[#4b5563]">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
