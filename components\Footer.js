import Image from 'next/image';
import Link from 'next/link';
import { FaEnvelopeSquare, FaFacebookSquare, FaInstagram, FaWhatsapp } from 'react-icons/fa';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#0e3169] text-white pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-10">
          <div>
            <div className="mb-6">
              <Image
                src="/images/logo/logo_bianco.png"
                alt="Di Nardo Soccorso Stradale"
                width={160}
                height={80}
                className="h-auto"
              />
            </div>
            <p className="mb-6">
              Servizio professionale di soccorso stradale e traino attivo 24 ore su 24, 
              7 giorni su 7. Interveniamo rapidamente in ogni situazione di emergenza.
            </p>
            <div className="flex space-x-4">
              <Link href="mailto:<EMAIL>" className="text-white hover:text-[#ffcc00]" aria-label="Email">
                <FaEnvelopeSquare size={24} />
              </Link>
              <Link href="https://wa.me/+393389566200" className="text-white hover:text-[#ffcc00]" aria-label="WhatsApp">
                <FaWhatsapp size={24} />
              </Link>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-bold mb-6 border-b border-[#23488a] pb-2">Link Utili</h3>
            <ul className="space-y-3">
              <li>
                <Link href="#servizi" className="hover:text-[#ffcc00] transition-colors">
                  Servizi
                </Link>
              </li>
              <li>
                <Link href="#chi-siamo" className="hover:text-[#ffcc00] transition-colors">
                  Chi Siamo
                </Link>
              </li>
              <li>
                <Link href="#contatti" className="hover:text-[#ffcc00] transition-colors">
                  Contatti
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-[#ffcc00] transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-xl font-bold mb-6 border-b border-[#23488a] pb-2">Orari di Servizio</h3>
            <ul className="space-y-3">
              <li className="flex justify-between">
                <span>Lunedì - Venerdì:</span>
                <span className="font-bold text-[#ffcc00]">24 ore</span>
              </li>
              <li className="flex justify-between">
                <span>Sabato:</span>
                <span className="font-bold text-[#ffcc00]">24 ore</span>
              </li>
              <li className="flex justify-between">
                <span>Domenica:</span>
                <span className="font-bold text-[#ffcc00]">24 ore</span>
              </li>
              <li className="flex justify-between">
                <span>Festivi:</span>
                <span className="font-bold text-[#ffcc00]">24 ore</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-[#23488a] pt-8 text-center">
          <p>&copy; {currentYear} Soccorso Stradale Di Nardo S.R.L.S. P.IVA 03003010596 | All rights reserved. | Powered by Programmarti </p>
        </div>
      </div>
    </footer>
  );
}
