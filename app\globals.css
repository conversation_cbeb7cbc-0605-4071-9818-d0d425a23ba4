@import "tailwindcss";

:root {
  --primary: #0e3169;
  --primary-light: #23488a;
  --secondary: #bcd4f6;
  --background: #f4f7fb;
  --text-primary: #0e3169;
  --text-secondary: #4b5563;
  --accent: #ffcc00;
  --white: #ffffff;
}

@theme inline {
  --color-primary: var(--primary);
  --color-primary-light: var(--primary-light);
  --color-secondary: var(--secondary);
  --color-background: var(--background);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-accent: var(--accent);
  --color-white: var(--white);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--text-secondary);
  font-family: var(--font-sans), sans-serif;
  overflow-x: hidden;
}

.btn-primary {
  @apply bg-[#0e3169] hover:bg-[#23488a] text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md;
}

.btn-cta {
  @apply bg-[#ffcc00] hover:bg-[#ffdd33] text-[#0e3169] font-bold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md;
}

.section-padding {
  @apply py-16 px-4 md:px-8 lg:px-16;
}

.card {
  @apply bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.section-title {
  @apply text-3xl md:text-4xl font-bold text-[#0e3169] mb-6;
}

.section-subtitle {
  @apply text-lg text-[#4b5563] mb-12 max-w-2xl mx-auto;
}

/* Classi specifiche per il carousel */
.carousel-image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

@media (max-width: 640px) {
  .carousel-image {
    object-fit: scale-down !important;
  }
}
