import { useState, useEffect } from 'react';
import Image from 'next/image';

const images = [
  "/images/slides/IMG-20240630-WA0010.jpg",
  "/images/slides/IMG-20240720-WA0032.jpg",
  "/images/slides/IMG-20240724-WA0002.jpg",
  "/images/slides/IMG-20240805-WA0104.jpg",
  "/images/slides/IMG-20240822-WA0007.jpg",
  "/images/slides/IMG-20240904-WA0009.jpg"
];

export default function ImageCarousel() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  // Controlla se è un dispositivo mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 640);
    };

    // Set iniziale
    checkIsMobile();

    // Aggiorna quando la finestra viene ridimensionata
    window.addEventListener('resize', checkIsMobile);
    
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative w-full h-[350px] sm:h-[400px] md:h-[500px] lg:h-[550px] overflow-hidden rounded-lg shadow-xl mx-auto">
      {images.map((image, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
            index === currentImageIndex ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <Image
            src={image}
            alt={`Slide ${index + 1}`}
            fill
            className={`w-full h-full rounded-lg ${isMobile ? 'object-contain' : 'object-cover md:object-cover'}`}
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 90vw, 80vw"
            priority={index === 0}
          />
        </div>
      ))}
      
      <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
        {images.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full ${
              index === currentImageIndex ? 'bg-[#ffcc00]' : 'bg-white bg-opacity-50'
            }`}
            onClick={() => setCurrentImageIndex(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
