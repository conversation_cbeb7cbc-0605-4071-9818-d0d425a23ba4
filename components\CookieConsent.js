"use client";

import { useState, useEffect } from 'react';

export default function CookieConsent() {
  const [showConsent, setShowConsent] = useState(false);
  
  useEffect(() => {
    // Verifica se l'utente ha già dato il consenso
    const hasConsent = localStorage.getItem('cookieConsent');
    if (!hasConsent) {
      setShowConsent(true);
      
      // Configurazione di base di Google Consent Mode v2
      window.dataLayer = window.dataLayer || [];
      function gtag() { dataLayer.push(arguments); }
      
      gtag('consent', 'default', {
        'ad_storage': 'denied',
        'analytics_storage': 'denied',
        'functionality_storage': 'denied',
        'personalization_storage': 'denied',
        'security_storage': 'granted', // Sempre consentito per motivi di sicurezza
        'wait_for_update': 500
      });
    }
  }, []);

  const handleAcceptAll = () => {
    localStorage.setItem('cookieConsent', 'all');
    updateConsent({
      'ad_storage': 'granted',
      'analytics_storage': 'granted',
      'functionality_storage': 'granted',
      'personalization_storage': 'granted',
      'security_storage': 'granted'
    });
    setShowConsent(false);
  };

  const handleAcceptNecessary = () => {
    localStorage.setItem('cookieConsent', 'necessary');
    updateConsent({
      'ad_storage': 'denied',
      'analytics_storage': 'denied',
      'functionality_storage': 'denied',
      'personalization_storage': 'denied',
      'security_storage': 'granted'
    });
    setShowConsent(false);
  };

  const handleCustomize = () => {
    // Qui potrebbe aprirsi un modale più dettagliato con opzioni specifiche
    // Per semplicità in questa implementazione, gestiamo solo le opzioni di base
    localStorage.setItem('cookieConsent', 'customized');
    setShowConsent(false);
  };

  const updateConsent = (consentOptions) => {
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('consent', 'update', consentOptions);
  };

  if (!showConsent) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white shadow-lg border-t border-gray-200 p-4">
      <div className="container mx-auto max-w-7xl">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="text-sm text-[#4b5563] flex-grow">            <p className="mb-2">
              <strong>Informativa sui Cookie</strong> - Questo sito utilizza cookie tecnici necessari al funzionamento e cookie di terze parti per offrirti una migliore esperienza.
            </p>
            <p>
              Cliccando su &quot;Accetta tutti&quot;, consenti l&apos;uso di tutti i cookie. Puoi scegliere &quot;Solo necessari&quot; per limitare i cookie al minimo indispensabile o personalizzare le tue preferenze.
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={handleAcceptNecessary}
              className="px-4 py-2 text-sm border border-[#0e3169] text-[#0e3169] rounded hover:bg-[#f4f7fb] transition-colors"
            >
              Solo necessari
            </button>

            <button
              onClick={handleAcceptAll}
              className="px-4 py-2 text-sm bg-[#0e3169] text-white rounded hover:bg-[#23488a] transition-colors"
            >
              Accetta tutti
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
