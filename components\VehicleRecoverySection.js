import Image from 'next/image';

const features = [
  {
    title: "Recupero Veicoli Incidentati",
    description: "Interveniamo rapidamente per recuperare veicoli coinvolti in incidenti stradali con attrezzature specializzate che garantiscono la massima sicurezza."
  },
  {
    title: "Trasporto Mezzi Pesanti",
    description: "Abbiamo le competenze e i mezzi necessari per il recupero e il trasporto di automezzi pesanti come camion, autobus e mezzi da lavoro."
  },
  {
    title: "Assistenza Completa",
    description: "Oltre al recupero, offriamo assistenza completa con trasporto del mezzo presso officine convenzionate e supporto per pratiche assicurative."
  },
  {    title: "Intervento Tempestivo",
    description: "Il nostro sistema operativo ci permette di raggiungere qualsiasi località in tempi rapidi, riducendo al minimo i disagi dell&apos;emergenza."
  }
];

export default function VehicleRecoverySection() {
  return (
    <section className="section-padding bg-[#f4f7fb]">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="section-title">Recupero di Ogni Tipo di Automezzo</h2>
          <p className="section-subtitle">
            La nostra flotta e il nostro personale specializzato sono in grado di intervenire su qualsiasi tipo di veicolo
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
          <div className="relative h-[400px] lg:h-[600px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/foto/20240130_065526.jpg"
              alt="Recupero veicoli Di Nardo"
              fill
              className="object-cover"
            />
          </div>

          <div className="space-y-8">
            {features.map((feature, index) => (
              <div key={index} className="card border-l-4 border-[#0e3169]">
                <h3 className="text-xl font-bold text-[#0e3169] mb-2">{feature.title}</h3>
                <p className="text-[#4b5563]">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
