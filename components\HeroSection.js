import Link from "next/link";
import Image from "next/image";
import { FaPhoneAlt, FaArrowRight } from "react-icons/fa";

export default function HeroSection() {
  return (
    <section className="pt-28 pb-16 md:pt-40 md:pb-24 bg-gradient-to-b from-[#f4f7fb] to-[#e1e9f5]">
      <div className="container mx-auto px-4 md:px-8">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-10">
          <div className="lg:w-1/2 text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#0e3169] mb-6">
              Soccorso Stradale <span className="text-[#ffcc00]">24/7</span>
            </h1>
            <p className="text-xl md:text-2xl text-[#4b5563] mb-8">
              Recupero e traino professionale per auto incidentate o in panne. 
              Interveniamo rapidamente in ogni situazione di emergenza.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="tel:+393917649488"
                className="flex items-center justify-center gap-2 btn-primary"
              >
                <FaPhoneAlt /> Chiama Ora
              </Link>
              <Link
                href="#servizi"
                className="flex items-center justify-center gap-2 btn-cta"
              >
                I Nostri Servizi <FaArrowRight />
              </Link>
            </div>
          </div>
          <div className="lg:w-1/2 relative h-[300px] md:h-[400px] w-full">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative w-full h-full">
                <Image
                  src="/images/slides/IMG-20240805-WA0104.jpg"
                  alt="Di Nardo Soccorso Stradale"
                  fill
                  className="object-cover rounded-xl shadow-lg"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
