import { Inter } from "next/font/google";
import "./globals.css";
import Script from "next/script";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata = {
  title: "Soccorso Stradale H24 - Di Nardo",
  description: "Servizio di soccorso stradale e traino attivo 24/7. Recupero auto incidentate o in panne, assistenza stradale immediata.",
  keywords: "soccorso stradale, traino auto, depannage, noleggio autogru, noleggio auto, auto sostitutiva, recupero veicoli, assistenza stradale 24h, auto in panne,soccorso Stradale - Leggero e Pesante, riavvio batteria, cambio gomme, rifornimento carburante, recupero veicoli incidentati, trasporto auto, soccorso stradale veloce, assistenza stradale professionale",
  manifest: "/favicon/site.webmanifest",
  icons: {
    icon: [
      { url: "/favicon/favicon.ico" },
      { url: "/favicon/favicon-96x96.png", sizes: "96x96", type: "image/png" },
    ],
    apple: [
      { url: "/favicon/apple-touch-icon.png" }
    ],
    other: [
      { url: "/favicon/web-app-manifest-192x192.png", sizes: "192x192", type: "image/png", rel: "apple-touch-icon" },
      { url: "/favicon/web-app-manifest-512x512.png", sizes: "512x512", type: "image/png", rel: "apple-touch-icon" },
    ],
  },
  themeColor: "#0e3169",
};

export default function RootLayout({ children }) {
  return (
    <html lang="it" className="scroll-smooth">
      <head>
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!="dataLayer"?"&l="+l:"";j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','GTM-XXXXXX');
            `,
          }}
        />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <noscript>
          <iframe 
            src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXXX"
            height="0" 
            width="0" 
            style={{ display: 'none', visibility: 'hidden' }}
          />
        </noscript>
        {children}
      </body>
    </html>
  );
}
