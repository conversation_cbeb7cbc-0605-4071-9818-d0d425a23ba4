'use client';

import { useState } from 'react';

export default function InviaPosizione() {
  const [link, setLink] = useState(null);
  const numeroWhatsapp = '+393389566200';

  const inviaPosizione = () => {
    if (!navigator.geolocation) {
      alert('Geolocalizzazione non supportata dal browser');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (pos) => {
        const lat = pos.coords.latitude.toFixed(6);
        const lon = pos.coords.longitude.toFixed(6);
        const text = `La mia posizione è: ${lat}, ${lon}`;
        const url = `https://wa.me/${numeroWhatsapp}?text=${encodeURIComponent(text)}`;
        setLink(url);
        window.open(url, '_blank');
      },
      (err) => {
        alert('Errore nel recupero della posizione: ' + err.message);
      }
    );
  };

  return (
    <div className="p-4">
      <button
        onClick={inviaPosizione}
        className="bg-green-500 text-white px-4 py-2 rounded"
      >
        Invia posizione via WhatsApp
      </button>
    </div>
  );
}
